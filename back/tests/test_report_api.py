#!/usr/bin/env python3
"""
周报月报API测试脚本
"""
import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:9099"

def get_captcha():
    """获取验证码"""
    response = requests.get(f"{BASE_URL}/captchaImage")
    if response.status_code == 200:
        result = response.json()
        return result.get('uuid'), result.get('captchaEnabled', True)
    return None, True

def login():
    """登录获取token"""
    # 先获取验证码信息
    uuid, captcha_enabled = get_captcha()

    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    # 如果需要验证码，添加验证码参数（这里我们跳过验证码验证，直接传空值）
    if captcha_enabled and uuid:
        login_data["code"] = ""  # 空验证码，可能会失败
        login_data["uuid"] = uuid

    # 添加referer头，模拟来自swagger的请求（开发模式下会跳过验证码验证）
    headers = {
        "referer": f"{BASE_URL}/docs"
    }

    # 使用form-data格式
    response = requests.post(f"{BASE_URL}/login", data=login_data, headers=headers)
    print(f"登录响应状态码: {response.status_code}")
    print(f"登录响应内容: {response.text}")

    if response.status_code == 200:
        result = response.json()
        # 检查是否直接返回了token信息
        if 'access_token' in result:
            token = result.get('access_token')
            print(f"登录成功，token: {token[:20]}...")
            return token
        elif result.get('code') == 200:
            token = result.get('data', {}).get('access_token')
            print(f"登录成功，token: {token[:20]}...")
            return token
        else:
            print(f"登录失败，错误信息: {result.get('msg', '未知错误')}")

    print("登录失败")
    return None

def test_create_weekly_report(token):
    """测试创建周报"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    report_data = {
        "reportType": "weekly",
        "reportDate": "2024-01-02",
        "summary": "本周完成了周报月报功能的开发，包括后端API和前端页面",
        "plan": "下周计划完成功能测试和优化",
        "problems": "在权限控制方面遇到了一些技术难题",
        "supportNeeded": "需要产品经理确认具体的权限需求",
        "isSaturated": "1",
        "remark": "这是一个测试周报"
    }

    response = requests.post(f"{BASE_URL}/report", json=report_data, headers=headers)
    print(f"创建周报 - 状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"创建周报 - 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        return result.get('data', {}).get('reportId')
    else:
        print(f"创建周报失败: {response.text}")
    return None

def test_create_monthly_report(token):
    """测试创建月报"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    report_data = {
        "reportType": "monthly",
        "reportDate": "2024-01-03",
        "summary": "本月完成了多个重要功能模块的开发",
        "plan": "下月计划重点进行系统优化和性能提升",
        "problems": "人力资源相对紧张",
        "supportNeeded": "需要增加开发人员",
        "remark": "这是一个测试月报"
    }

    response = requests.post(f"{BASE_URL}/report", json=report_data, headers=headers)
    print(f"创建月报 - 状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"创建月报 - 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        return result.get('data', {}).get('reportId')
    else:
        print(f"创建月报失败: {response.text}")
    return None

def test_get_report_list(token):
    """测试获取报告列表"""
    headers = {
        "Authorization": f"Bearer {token}"
    }

    # 测试获取周报列表
    response = requests.get(f"{BASE_URL}/report/page?reportType=weekly&pageNum=1&pageSize=10", headers=headers)
    print(f"获取周报列表 - 状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"获取周报列表 - 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

    # 测试获取月报列表
    response = requests.get(f"{BASE_URL}/report/page?reportType=monthly&pageNum=1&pageSize=10", headers=headers)
    print(f"获取月报列表 - 状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"获取月报列表 - 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_get_report_detail(token, report_id):
    """测试获取报告详情"""
    if not report_id:
        print("没有报告ID，跳过详情测试")
        return

    headers = {
        "Authorization": f"Bearer {token}"
    }

    response = requests.get(f"{BASE_URL}/report/{report_id}", headers=headers)
    print(f"获取报告详情 - 状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"获取报告详情 - 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取报告详情失败: {response.text}")

def main():
    """主测试函数"""
    print("开始测试周报月报API...")

    # 1. 登录
    token = login()
    if not token:
        return

    print("\n" + "="*50)

    # 2. 创建周报
    print("测试创建周报...")
    weekly_report_id = test_create_weekly_report(token)

    print("\n" + "="*50)

    # 3. 创建月报
    print("测试创建月报...")
    monthly_report_id = test_create_monthly_report(token)

    print("\n" + "="*50)

    # 4. 获取报告列表
    print("测试获取报告列表...")
    test_get_report_list(token)

    print("\n" + "="*50)

    # 5. 获取报告详情
    print("测试获取报告详情...")
    test_get_report_detail(token, weekly_report_id)

    print("\n测试完成！")

if __name__ == "__main__":
    main()
